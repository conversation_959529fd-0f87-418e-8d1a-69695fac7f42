#!/bin/bash

echo "=========================================="
echo "NTT MPI Performance Experiment"
echo "=========================================="

# Create results directory
mkdir -p experiment_results
cd experiment_results

# CSV header
echo "Implementation,Problem_Size,Execution_Time_ms,Num_Processes,Test_Passed" > performance_results.csv

PROCESSES=4

echo "Running performance experiments with $PROCESSES processes..."
echo

# Function to extract timing and correctness from output
extract_results() {
    local output="$1"
    local impl_name="$2"
    
    echo "$output" | grep "测试用例" | while read line; do
        if echo "$line" | grep -q "测试用例"; then
            # Extract test case number, problem size, and time
            test_case=$(echo "$line" | sed 's/.*测试用例 \([0-9]\+\).*/\1/')
            problem_size=$(echo "$line" | sed 's/.*n=\([0-9]\+\).*/\1/')
            time_ms=$(echo "$line" | sed 's/.*: \([0-9.]\+\) ms/\1/')
            
            # Check if test passed (look for "正确" in previous lines)
            test_passed="False"
            if echo "$output" | grep -B1 "$line" | grep -q "多项式乘法结果正确"; then
                test_passed="True"
            fi
            
            echo "$impl_name,$problem_size,$time_ms,$PROCESSES,$test_passed" >> performance_results.csv
        fi
    done
}

# Test 1: CRT+SIMD+OpenMP+MPI
echo "1. Testing CRT+SIMD+OpenMP+MPI Implementation..."
if [ -f "../crt_simd_openmp_mpi_final" ]; then
    output=$(timeout 180 mpirun -np $PROCESSES ../crt_simd_openmp_mpi_final 2>/dev/null)
    extract_results "$output" "CRT+SIMD+OpenMP+MPI"
    echo "   ✅ Completed"
else
    echo "   ❌ Executable not found"
fi

# Test 2: Radix-8 NTT
echo "2. Testing Radix-8 NTT Implementation..."
if [ -f "../radix8_mpi_true_final2" ]; then
    output=$(timeout 180 mpirun -np $PROCESSES ../radix8_mpi_true_final2 2>/dev/null)
    extract_results "$output" "Radix-8 NTT"
    echo "   ✅ Completed"
else
    echo "   ❌ Executable not found"
fi

# Test 3: Hybrid Parallel Strategy
echo "3. Testing Hybrid Parallel Strategy Implementation..."
if [ -f "../hybrid_parallel_mpi_fixed" ]; then
    output=$(timeout 180 mpirun -np $PROCESSES ../hybrid_parallel_mpi_fixed 2>/dev/null)
    extract_results "$output" "Hybrid Parallel Strategy"
    echo "   ✅ Completed"
else
    echo "   ❌ Executable not found"
fi

# Test 4: Baseline Radix-4 (if available)
echo "4. Testing Baseline Radix-4 Implementation..."
if [ -f "../barrett_radix4_mpi" ]; then
    output=$(timeout 180 mpirun -np $PROCESSES ../barrett_radix4_mpi 2>/dev/null)
    extract_results "$output" "Radix-4 Baseline"
    echo "   ✅ Completed"
else
    echo "   ❌ Executable not found"
fi

echo
echo "Performance data collection completed!"
echo "Results saved to: experiment_results/performance_results.csv"
echo

# Generate Python visualization script
cat > visualize_performance.py << 'EOF'
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Read the CSV data
try:
    df = pd.read_csv('performance_results.csv')
    print("Data loaded successfully!")
    print(f"Total records: {len(df)}")
    print(f"Implementations: {df['Implementation'].unique()}")
    print(f"Problem sizes: {sorted(df['Problem_Size'].unique())}")
except Exception as e:
    print(f"Error loading data: {e}")
    exit(1)

# Set up the plotting style
plt.style.use('default')
sns.set_palette("husl")

# Create figure with subplots
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('NTT MPI Implementation Performance Analysis', fontsize=16, fontweight='bold')

# 1. Execution Time vs Problem Size
ax1 = axes[0, 0]
for impl in df['Implementation'].unique():
    impl_data = df[df['Implementation'] == impl]
    if len(impl_data) > 0:
        ax1.plot(impl_data['Problem_Size'], impl_data['Execution_Time_ms'], 
                 marker='o', linewidth=2, markersize=8, label=impl)

ax1.set_xlabel('Problem Size (n)', fontsize=12)
ax1.set_ylabel('Execution Time (ms)', fontsize=12)
ax1.set_title('Performance vs Problem Size', fontsize=14, fontweight='bold')
ax1.set_xscale('log')
ax1.set_yscale('log')
ax1.grid(True, alpha=0.3)
ax1.legend()

# 2. Performance Comparison Bar Chart
ax2 = axes[0, 1]
# Group by implementation and calculate average performance
avg_performance = df.groupby('Implementation')['Execution_Time_ms'].mean().sort_values()
implementations = avg_performance.index
times = avg_performance.values

colors = sns.color_palette("husl", len(implementations))
bars = ax2.bar(range(len(implementations)), times, color=colors)
ax2.set_xlabel('Implementation', fontsize=12)
ax2.set_ylabel('Average Execution Time (ms)', fontsize=12)
ax2.set_title('Average Performance Comparison', fontsize=14, fontweight='bold')
ax2.set_xticks(range(len(implementations)))
ax2.set_xticklabels(implementations, rotation=45, ha='right')
ax2.grid(True, alpha=0.3, axis='y')

# Add value labels on bars
for i, bar in enumerate(bars):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{height:.1f}ms', ha='center', va='bottom', fontsize=10)

# 3. Speedup Analysis (relative to slowest implementation)
ax3 = axes[1, 0]
baseline_impl = avg_performance.index[-1]  # Slowest implementation
baseline_data = df[df['Implementation'] == baseline_impl]

for impl in df['Implementation'].unique():
    impl_data = df[df['Implementation'] == impl]
    speedup = []
    problem_sizes = []
    
    for size in impl_data['Problem_Size'].unique():
        if len(baseline_data[baseline_data['Problem_Size'] == size]) > 0:
            baseline_time = baseline_data[baseline_data['Problem_Size'] == size]['Execution_Time_ms'].iloc[0]
            impl_time = impl_data[impl_data['Problem_Size'] == size]['Execution_Time_ms'].iloc[0]
            speedup.append(baseline_time / impl_time)
            problem_sizes.append(size)
    
    if len(speedup) > 0:
        ax3.plot(problem_sizes, speedup, marker='s', linewidth=2, markersize=8, label=impl)

ax3.set_xlabel('Problem Size (n)', fontsize=12)
ax3.set_ylabel('Speedup Factor', fontsize=12)
ax3.set_title(f'Speedup Relative to {baseline_impl}', fontsize=14, fontweight='bold')
ax3.set_xscale('log')
ax3.axhline(y=1, color='red', linestyle='--', alpha=0.7, label='Baseline')
ax3.grid(True, alpha=0.3)
ax3.legend()

# 4. Test Success Rate
ax4 = axes[1, 1]
success_rate = df.groupby('Implementation')['Test_Passed'].apply(lambda x: (x == 'True').sum() / len(x) * 100)
implementations = success_rate.index
rates = success_rate.values

colors = ['green' if rate == 100 else 'orange' if rate >= 75 else 'red' for rate in rates]
bars = ax4.bar(range(len(implementations)), rates, color=colors)
ax4.set_xlabel('Implementation', fontsize=12)
ax4.set_ylabel('Test Success Rate (%)', fontsize=12)
ax4.set_title('Correctness Validation', fontsize=14, fontweight='bold')
ax4.set_xticks(range(len(implementations)))
ax4.set_xticklabels(implementations, rotation=45, ha='right')
ax4.set_ylim(0, 105)
ax4.grid(True, alpha=0.3, axis='y')

# Add value labels on bars
for i, bar in enumerate(bars):
    height = bar.get_height()
    ax4.text(bar.get_x() + bar.get_width()/2., height + 2,
             f'{height:.0f}%', ha='center', va='bottom', fontsize=10)

plt.tight_layout()
plt.savefig('ntt_performance_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

# Generate summary statistics
print("\n=== Performance Analysis Summary ===")
print(f"Total implementations tested: {df['Implementation'].nunique()}")
print(f"Problem sizes tested: {sorted(df['Problem_Size'].unique())}")
print(f"Total test cases: {len(df)}")

print("\n=== Performance Ranking (Average) ===")
for i, (impl, avg_time) in enumerate(avg_performance.items(), 1):
    success_rate = (df[df['Implementation'] == impl]['Test_Passed'] == 'True').sum()
    total_tests = len(df[df['Implementation'] == impl])
    print(f"{i}. {impl}: {avg_time:.2f}ms average ({success_rate}/{total_tests} tests passed)")

print("\n=== Best Performance by Problem Size ===")
for size in sorted(df['Problem_Size'].unique()):
    size_data = df[df['Problem_Size'] == size]
    if len(size_data) > 0:
        best = size_data.loc[size_data['Execution_Time_ms'].idxmin()]
        print(f"n={size}: {best['Implementation']} ({best['Execution_Time_ms']:.2f}ms)")

print("\nVisualization saved as: ntt_performance_analysis.png")
EOF

echo "Python visualization script created: visualize_performance.py"
echo
echo "To generate performance visualizations, run:"
echo "cd experiment_results"
echo "python3 visualize_performance.py"
echo
echo "=========================================="
