#include <bits/stdc++.h>
#include <mpi.h>

using namespace std;

void fRead(int *a, int *b, int *n, int *p, int input_id) {
    string path = "nttdata/" + to_string(input_id) + ".in";
    ifstream fin(path);
    if (!fin.is_open()) {
        if (MPI_COMM_WORLD != MPI_COMM_NULL) {
            cerr << "Fatal: Failed to open input file: " << path << endl;
            MPI_Abort(MPI_COMM_WORLD, 1);
        }
        exit(1);
    }
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}

void fCheck(int *ab, int n, int input_id) {
    string path = "nttdata/" + to_string(input_id) + ".out";
    ifstream fin(path);
    if (!fin.is_open()) {
        cerr << "Fatal: Failed to open output file for checking: " << path << endl;
        MPI_Abort(MPI_COMM_WORLD, 1);
    }
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; fin >> x;
        if (x != ab[i]) {
            cout << "多项式乘法结果错误\n";
            return;
        }
    }
    cout << "多项式乘法结果正确\n";
}

int mod_pow(int base, int exp, int mod) {
    int result = 1;
    base %= mod;
    while (exp > 0) {
        if (exp % 2 == 1) result = (1LL * result * base) % mod;
        base = (1LL * base * base) % mod;
        exp /= 2;
    }
    return result;
}

void bitrev(int *a, int n) {
    if (n <= 1) return;
    int lg = __builtin_ctz(n);
    for (int i = 0; i < n; ++i) {
        int rev = 0;
        for (int j = 0; j < lg; ++j) {
            if ((i >> j) & 1) {
                rev |= 1 << (lg - 1 - j);
            }
        }
        if (i < rev) {
            swap(a[i], a[rev]);
        }
    }
}

void ntt_mpi(int *a, int n, bool inv, int p, int rank, int size, MPI_Comm comm) {
    if (n <= 1) return;

    if (rank == 0) {
        bitrev(a, n);
    }
    MPI_Bcast(a, n, MPI_INT, 0, comm);

    int log_n = __builtin_ctz(n);

    for (int log_len = 1; log_len <= log_n; ) {
        int len = 1 << log_len;
        int current_radix_log;
        
        if (log_n - log_len >= 2) {
            current_radix_log = 3; 
        } else if (log_n - log_len >= 1) {
            current_radix_log = 2;
        } else {
            current_radix_log = 1;
        }
        
        if (log_n - log_len +1 < current_radix_log) {
            current_radix_log = log_n - log_len + 1;
        }


        len = 1 << (log_len -1);
        int radix = 1 << current_radix_log;
        int next_len = len * radix;

        int wn = mod_pow(3, (p - 1) / next_len, p);
        if (inv) wn = mod_pow(wn, p - 2, p);

        int total_chunks = n / next_len;
        int chunks_per_proc = total_chunks / size;
        int remainder_chunks = total_chunks % size;
        int my_start_chunk = rank * chunks_per_proc + min(rank, remainder_chunks);
        int my_num_chunks = chunks_per_proc + (rank < remainder_chunks ? 1 : 0);

        for (int k = 0; k < my_num_chunks; ++k) {
            int i = (my_start_chunk + k) * next_len;
            if (current_radix_log == 1) { // Radix-2
                for (int j = 0; j < len; ++j) {
                    int w = mod_pow(wn, j, p);
                    int u = a[i + j];
                    int v = (1LL * a[i + j + len] * w) % p;
                    a[i + j] = (u + v) % p;
                    a[i + j + len] = (u - v + p) % p;
                }
            } else if (current_radix_log == 2) { // Radix-4
                int J = mod_pow(wn, len, p);
                 for (int j = 0; j < len; ++j) {
                    int w = mod_pow(wn, j, p);
                    int w2 = (1LL * w * w) % p;
                    int w3 = (1LL * w2 * w) % p;

                    int x0 = a[i+j];
                    int x1 = (1LL * a[i+j+len] * w) % p;
                    int x2 = (1LL * a[i+j+2*len] * w2) % p;
                    int x3 = (1LL * a[i+j+3*len] * w3) % p;
                    
                    int t0 = (x0 + x2) % p;
                    int t1 = (x0 - x2 + p) % p;
                    int t2 = (x1 + x3) % p;
                    int t3 = (1LL * (x1 - x3 + p) * J) % p;

                    a[i+j] = (t0 + t2) % p;
                    a[i+j+len] = (t1 + t3) % p;
                    a[i+j+2*len] = (t0 - t2 + p) % p;
                    a[i+j+3*len] = (t1 - t3 + p) % p;
                }
            } else { // Radix-8
                 int J = mod_pow(wn, len, p);
                 int J2 = (1LL*J*J)%p;
                 int J3 = (1LL*J2*J)%p;
                 for (int j = 0; j < len; ++j) {
                    int w = mod_pow(wn, j, p);
                    long long Ws[8]; Ws[0] = 1;
                    for(int l=1; l<8; ++l) Ws[l] = (Ws[l-1] * w) % p;
                    
                    int x[8]; for(int l=0; l<8; ++l) x[l] = (1LL * a[i+j+l*len] * Ws[l]) % p;

                    int y[8];
                    for(int l=0; l<4; ++l) { y[l] = (x[l] + x[l+4])%p; y[l+4] = (x[l]-x[l+4]+p)%p; }
                    
                    int z[8];
                    z[0]=(y[0]+y[2])%p; z[1]=(y[1]+y[3])%p; z[2]=(y[0]-y[2]+p)%p; z[3]=(1LL*(y[1]-y[3]+p)*J)%p;
                    z[4]=(y[4]+y[6])%p; z[5]=(1LL*(y[5]+y[7])*J2)%p; z[6]=(1LL*(y[4]-y[6]+p)*J)%p; z[7]=(1LL*(y[5]-y[7]+p)*J3)%p;

                    a[i+j]         = (z[0]+z[1]) % p;
                    a[i+j+len]     = (z[4]+z[5]) % p;
                    a[i+j+2*len] = (z[2]+z[3]) % p;
                    a[i+j+3*len] = (z[6]+z[7]) % p;
                    a[i+j+4*len] = (z[0]-z[1]+p) % p;
                    a[i+j+5*len] = (z[4]-z[5]+p) % p;
                    a[i+j+6*len] = (z[2]-z[3]+p) % p;
                    a[i+j+7*len] = (z[6]-z[7]+p) % p;
                }
            }
        }
        
        if (my_num_chunks > 0) {
            MPI_Allgather(MPI_IN_PLACE, 0, MPI_DATATYPE_NULL, a + my_start_chunk * next_len, my_num_chunks * next_len, MPI_INT, comm);
        }
        MPI_Bcast(a, n, MPI_INT, 0, comm);


        log_len += current_radix_log;
    }

    if (inv) {
        if (rank == 0) {
            int invN = mod_pow(n, p - 2, p);
            for (int i = 0; i < n; ++i) {
                a[i] = (1LL * a[i] * invN) % p;
            }
        }
        MPI_Bcast(a, n, MPI_INT, 0, comm);
    }
}

void poly_mul_mpi(const int *A_const, const int *B_const, int *C, int n_orig, int p,
                         int rank, int size, MPI_Comm comm) {
    if (n_orig <= 0) return;

    int N = 1;
    while (N < 2 * n_orig) N <<= 1;

    vector<int> pa(N, 0), pb(N, 0);
    for (int i = 0; i < n_orig; ++i) {
        pa[i] = ((A_const[i] % p) + p) % p;
        pb[i] = ((B_const[i] % p) + p) % p;
    }

    ntt_mpi(pa.data(), N, false, p, rank, size, comm);
    ntt_mpi(pb.data(), N, false, p, rank, size, comm);

    int items_per_rank = N / size;
    int my_start = rank * items_per_rank;
    int my_items = (rank == size - 1) ? (N - my_start) : items_per_rank;

    for (int i = 0; i < my_items; ++i) {
        int global_idx = my_start + i;
        pa[global_idx] = (1LL * pa[global_idx] * pb[global_idx]) % p;
    }
    
    MPI_Allgather(MPI_IN_PLACE, 0, MPI_DATATYPE_NULL, pa.data() + my_start, my_items, MPI_INT, comm);

    ntt_mpi(pa.data(), N, true, p, rank, size, comm);

    if (rank == 0) {
        int result_size = 2 * n_orig - 1;
        if (result_size > 0) {
            memcpy(C, pa.data(), sizeof(int) * result_size);
        }
    }
}

int main(int argc, char** argv) {
    MPI_Init(&argc, &argv);
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);

    if (rank == 0) {
        cout << "Mixed-Radix NTT MPI (Radix-8, 4, 2) 实现\n";
        cout << "进程数: " << size << "\n";
        cout << "========================================\n";
    }

    vector<int> a_storage(300000), b_storage(300000);
    vector<int> ab_storage(600000);

    for (int id = 0; id <= 3; ++id) {
        int n = 0, p = 0;

        if (rank == 0) {
            fRead(a_storage.data(), b_storage.data(), &n, &p, id);
        }

        MPI_Bcast(&n, 1, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(&p, 1, MPI_INT, 0, MPI_COMM_WORLD);
        if (n > 0) {
            MPI_Bcast(a_storage.data(), n, MPI_INT, 0, MPI_COMM_WORLD);
            MPI_Bcast(b_storage.data(), n, MPI_INT, 0, MPI_COMM_WORLD);
        }
        
        MPI_Barrier(MPI_COMM_WORLD);
        double t0 = MPI_Wtime();

        poly_mul_mpi(a_storage.data(), b_storage.data(), ab_storage.data(), n, p, rank, size, MPI_COMM_WORLD);

        MPI_Barrier(MPI_COMM_WORLD);
        double t1 = MPI_Wtime();

        if (rank == 0) {
            fCheck(ab_storage.data(), n, id);
            cout << "测试用例 " << id << " (n=" << n << ", p=" << p << "): "
                 << fixed << setprecision(3) << (t1 - t0) * 1000 << " ms\n";
        }
    }

    MPI_Finalize();
    return 0;
}
