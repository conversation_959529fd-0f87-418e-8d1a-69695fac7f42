#include <bits/stdc++.h>
#include <mpi.h>
#include <omp.h>

using namespace std;

void fRead(int *a, int *b, int *n, int *p, int input_id) {
    string path = "nttdata/" + to_string(input_id) + ".in";
    ifstream fin(path);
    if (!fin.is_open()) {
        if (MPI_COMM_WORLD != MPI_COMM_NULL) {
            cerr << "Fatal: Failed to open input file: " << path << endl;
            MPI_Abort(MPI_COMM_WORLD, 1);
        }
        exit(1);
    }
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}

void fCheck(int *ab, int n, int input_id) {
    string path = "nttdata/" + to_string(input_id) + ".out";
    ifstream fin(path);
    if (!fin.is_open()) {
        cerr << "Fatal: Failed to open output file for checking: " << path << endl;
        MPI_Abort(MPI_COMM_WORLD, 1);
    }
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; fin >> x;
        if (x != ab[i]) {
            cout << "多项式乘法结果错误\n";
            return;
        }
    }
    cout << "多项式乘法结果正确\n";
}

int mod_pow(int base, int exp, int mod) {
    long long res = 1;
    base %= mod;
    while (exp > 0) {
        if (exp % 2 == 1) res = (res * base) % mod;
        base = (1LL * base * base) % mod;
        exp /= 2;
    }
    return res;
}

void bitrev_omp(vector<int>& a) {
    int n = a.size();
    if (n <= 1) return;
    vector<int> rev(n);
    int lg = __builtin_ctz(n);
    #pragma omp parallel for
    for (int i = 0; i < n; i++) {
        int r = 0;
        for (int j = 0; j < lg; j++) {
            if ((i >> j) & 1) {
                r |= 1 << (lg - 1 - j);
            }
        }
        rev[i] = r;
    }

    #pragma omp parallel for
    for (int i = 0; i < n; i++) {
        if (i < rev[i]) {
            swap(a[i], a[rev[i]]);
        }
    }
}

void ntt_mpi(vector<int>& a, bool inverse, int p, int rank, int size, MPI_Comm comm) {
    int n = a.size();
    if (n <= 1) return;

    if (rank == 0) {
        bitrev_omp(a);
    }
    MPI_Bcast(a.data(), n, MPI_INT, 0, comm);

    for (int len = 2; len <= n; len <<= 1) {
        int wlen = mod_pow(3, (p - 1) / len, p);
        if (inverse) {
            wlen = mod_pow(wlen, p - 2, p);
        }

        int blocks_per_proc = (n / len) / size;
        int remainder_blocks = (n / len) % size;
        int my_start_block = rank * blocks_per_proc + min(rank, remainder_blocks);
        int my_num_blocks = blocks_per_proc + (rank < remainder_blocks ? 1 : 0);

        if (my_num_blocks > 0) {
            vector<int> local_block_data(my_num_blocks * len);
            memcpy(local_block_data.data(), a.data() + my_start_block * len, local_block_data.size() * sizeof(int));
            
            #pragma omp parallel for
            for (int k = 0; k < my_num_blocks; ++k) {
                int w = 1;
                int block_offset = k * len;
                for (int j = 0; j < len / 2; ++j) {
                    int u = local_block_data[block_offset + j];
                    int v = (1LL * local_block_data[block_offset + j + len / 2] * w) % p;
                    local_block_data[block_offset + j] = (u + v) % p;
                    local_block_data[block_offset + j + len / 2] = (u - v + p) % p;
                    w = (1LL * w * wlen) % p;
                }
            }
            memcpy(a.data() + my_start_block * len, local_block_data.data(), local_block_data.size() * sizeof(int));
        }

        vector<int> recvcounts(size);
        vector<int> displs(size);
        for(int i=0; i<size; ++i) {
            recvcounts[i] = (i < remainder_blocks ? blocks_per_proc + 1 : blocks_per_proc) * len;
            displs[i] = (i * blocks_per_proc + min(i, remainder_blocks)) * len;
        }

        if (my_num_blocks > 0) {
             MPI_Allgatherv(MPI_IN_PLACE, 0, MPI_DATATYPE_NULL, a.data(), recvcounts.data(), displs.data(), MPI_INT, comm);
        } else {
             MPI_Allgatherv(a.data(), 0, MPI_INT, a.data(), recvcounts.data(), displs.data(), MPI_INT, comm);
        }
    }

    if (inverse) {
        if (rank == 0) {
            int n_inv = mod_pow(n, p - 2, p);
            #pragma omp parallel for
            for (int i = 0; i < n; ++i) {
                a[i] = (1LL * a[i] * n_inv) % p;
            }
        }
        MPI_Bcast(a.data(), n, MPI_INT, 0, comm);
    }
}

void poly_mul_mpi(const vector<int>& A_const, const vector<int>& B_const, vector<int>& C, int n_orig, int p,
                         int rank, int size, MPI_Comm comm) {
    if (n_orig <= 0) return;

    int N = 1;
    while (N < 2 * n_orig) N <<= 1;

    vector<int> pa(N, 0), pb(N, 0);
    
    if (rank == 0) {
        for (int i = 0; i < n_orig; ++i) {
            pa[i] = ((A_const[i] % p) + p) % p;
            pb[i] = ((B_const[i] % p) + p) % p;
        }
    }
    MPI_Bcast(pa.data(), N, MPI_INT, 0, comm);
    MPI_Bcast(pb.data(), N, MPI_INT, 0, comm);

    ntt_mpi(pa, false, p, rank, size, comm);
    ntt_mpi(pb, false, p, rank, size, comm);

    int items_per_rank = N / size;
    int my_start = rank * items_per_rank;
    int my_items = (rank == size - 1) ? (N - my_start) : items_per_rank;

    #pragma omp parallel for
    for (int i = 0; i < my_items; ++i) {
        int global_idx = my_start + i;
        pa[global_idx] = (1LL * pa[global_idx] * pb[global_idx]) % p;
    }
    
    vector<int> recvcounts(size);
    vector<int> displs(size);
    for(int i=0; i<size; ++i) {
        recvcounts[i] = (i == size - 1) ? (N - i*items_per_rank) : items_per_rank;
        displs[i] = i*items_per_rank;
    }
    
    MPI_Allgatherv(MPI_IN_PLACE, 0, MPI_DATATYPE_NULL, pa.data(), recvcounts.data(), displs.data(), MPI_INT, comm);

    ntt_mpi(pa, true, p, rank, size, comm);

    if (rank == 0) {
        C.assign(pa.begin(), pa.begin() + 2 * n_orig - 1);
    }
}

int main(int argc, char** argv) {
    MPI_Init(&argc, &argv);
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);

    if (rank == 0) {
        cout << "Radix-2 NTT with MPI + OpenMP\n";
        cout << "Processes: " << size << ", Threads/Process: " << omp_get_max_threads() << "\n";
        cout << "========================================\n";
    }

    vector<int> a, b, ab;
    int n = 0, p = 0;

    for (int id = 0; id <= 3; ++id) {
        if (rank == 0) {
            vector<int> a_full(300000), b_full(300000);
            fRead(a_full.data(), b_full.data(), &n, &p, id);
            a.assign(a_full.begin(), a_full.begin() + n);
            b.assign(b_full.begin(), b_full.begin() + n);
        }

        MPI_Bcast(&n, 1, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(&p, 1, MPI_INT, 0, MPI_COMM_WORLD);
        if (rank != 0) {
           a.resize(n);
           b.resize(n);
        }
        MPI_Bcast(a.data(), n, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(b.data(), n, MPI_INT, 0, MPI_COMM_WORLD);
        
        ab.resize(2 * n - 1);

        MPI_Barrier(MPI_COMM_WORLD);
        double t0 = MPI_Wtime();

        poly_mul_mpi(a, b, ab, n, p, rank, size, MPI_COMM_WORLD);

        MPI_Barrier(MPI_COMM_WORLD);
        double t1 = MPI_Wtime();

        if (rank == 0) {
            fCheck(ab.data(), n, id);
            cout << "Test case " << id << " (n=" << n << ", p=" << p << "): "
                 << fixed << setprecision(3) << (t1 - t0) * 1000 << " ms\n";
        }
    }

    MPI_Finalize();
    return 0;
}
