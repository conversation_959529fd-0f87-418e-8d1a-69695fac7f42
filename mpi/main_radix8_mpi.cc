#include <bits/stdc++.h>
#include <mpi.h>

using namespace std;

void fRead(int *a, int *b, int *n, int *p, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".in";
    ifstream fin(path);
    fin >> *n >> *p;
    for (int i = 0; i < *n; ++i) fin >> a[i];
    for (int i = 0; i < *n; ++i) fin >> b[i];
}

void fCheck(int *ab, int n, int input_id) {
    string path = "../nttdata/" + to_string(input_id) + ".out";
    ifstream fin(path);
    for (int i = 0; i < 2 * n - 1; ++i) {
        int x; fin >> x;
        if (x != ab[i]) {
            cout << "多项式乘法结果错误\n";
            return;
        }
    }
    cout << "多项式乘法结果正确\n";
}

int mod_pow(int base, int exp, int mod) {
    int result = 1;
    while (exp > 0) {
        if (exp % 2 == 1) result = (1LL * result * base) % mod;
        base = (1LL * base * base) % mod;
        exp /= 2;
    }
    return result;
}

void bitrev(int *a, int n) {
    int lg = __builtin_ctz(n);
    for (int i = 0; i < n; ++i) {
        int rev = 0;
        for (int j = 0; j < lg; ++j) {
            if (i >> j & 1) rev |= 1 << (lg - 1 - j);
        }
        if (i < rev) swap(a[i], a[rev]);
    }
}

void radix8_butterfly(int *a, int base_idx, int w, int p) {
    int w2 = (1LL * w * w) % p;
    int w3 = (1LL * w2 * w) % p;
    int w4 = (1LL * w2 * w2) % p;
    int w5 = (1LL * w4 * w) % p;
    int w6 = (1LL * w3 * w3) % p;
    int w7 = (1LL * w6 * w) % p;

    int x0 = a[base_idx];
    int x1 = (1LL * a[base_idx + 1] * w) % p;
    int x2 = (1LL * a[base_idx + 2] * w2) % p;
    int x3 = (1LL * a[base_idx + 3] * w3) % p;
    int x4 = (1LL * a[base_idx + 4] * w4) % p;
    int x5 = (1LL * a[base_idx + 5] * w5) % p;
    int x6 = (1LL * a[base_idx + 6] * w6) % p;
    int x7 = (1LL * a[base_idx + 7] * w7) % p;

    int t0 = (x0 + x4) % p;
    int t1 = (x0 - x4 + p) % p;
    int t2 = (x2 + x6) % p;
    int t3 = (x2 - x6 + p) % p;
    int t4 = (x1 + x5) % p;
    int t5 = (x1 - x5 + p) % p;
    int t6 = (x3 + x7) % p;
    int t7 = (x3 - x7 + p) % p;

    int u0 = (t0 + t2) % p;
    int u1 = (t0 - t2 + p) % p;
    int u2 = (t1 + t3) % p;
    int u3 = (t1 - t3 + p) % p;
    int u4 = (t4 + t6) % p;
    int u5 = (t4 - t6 + p) % p;
    int u6 = (t5 + t7) % p;
    int u7 = (t5 - t7 + p) % p;

    a[base_idx]     = (u0 + u4) % p;
    a[base_idx + 1] = (u2 + u6) % p;
    a[base_idx + 2] = (u1 + u5) % p;
    a[base_idx + 3] = (u3 + u7) % p;
    a[base_idx + 4] = (u0 - u4 + p) % p;
    a[base_idx + 5] = (u2 - u6 + p) % p;
    a[base_idx + 6] = (u1 - u5 + p) % p;
    a[base_idx + 7] = (u3 - u7 + p) % p;
}

void radix8_layers_mpi(int *a, int n, bool inv, int p, int rank, int size, MPI_Comm comm) {
    for (int len = 8; len <= n; len <<= 3) {
        int wn = mod_pow(3, (p - 1) / len, p);
        if (inv) wn = mod_pow(wn, p - 2, p);

        int total_blocks = n / len;
        if (total_blocks == 0) continue;

        int blocks_per_proc = total_blocks / size;
        int remainder_blocks = total_blocks % size;
        int my_blocks = blocks_per_proc + (rank < remainder_blocks ? 1 : 0);
        int start_block = rank * blocks_per_proc + min(rank, remainder_blocks);

        for (int block = 0; block < my_blocks; ++block) {
            int block_start = (start_block + block) * len;
            if (block_start + len > n) break;

            for (int j = 0; j < len; j += 8) {
                if (block_start + j + 7 >= n) break;

                int w = mod_pow(wn, j, p);
                radix8_butterfly(a, block_start + j, w, p);
            }
        }

        vector<int> recvcounts(size);
        vector<int> displs(size);
        for (int r = 0; r < size; ++r) {
            int r_blocks = blocks_per_proc + (r < remainder_blocks ? 1 : 0);
            recvcounts[r] = r_blocks * len;
            displs[r] = (r * blocks_per_proc + min(r, remainder_blocks)) * len;
        }

        int sendcnt = my_blocks * len;
        if (sendcnt > 0 && start_block * len < n) {
            int actual_size = min(sendcnt, n - start_block * len);
            vector<int> sendbuf(sendcnt, 0);
            if (actual_size > 0) {
                memcpy(sendbuf.data(), a + start_block * len, sizeof(int) * actual_size);
            }
            MPI_Allgatherv(sendbuf.data(), sendcnt, MPI_INT, a, recvcounts.data(), displs.data(), MPI_INT, comm);
        } else {
            MPI_Allgatherv(nullptr, 0, MPI_INT, a, recvcounts.data(), displs.data(), MPI_INT, comm);
        }
    }
}

void radix2_layer_serial(int *a, int n, bool inv, int p) {
    int h = n >> 1;
    int wn = mod_pow(3, (p-1)/n, p);
    if (inv) wn = mod_pow(wn, p-2, p);
    int w = 1;

    for (int j = 0; j < h; ++j) {
        int u = a[j];
        int v = (1LL * a[j+h] * w) % p;
        a[j] = (u + v) % p;
        a[j+h] = (u - v + p) % p;
        w = (1LL * w * wn) % p;
    }
}

void radix4_layer_serial(int *a, int n, bool inv, int p) {
    int quarter = n >> 2;
    int wn = mod_pow(3, (p-1)/n, p);
    if (inv) wn = mod_pow(wn, p-2, p);
    int J = mod_pow(wn, quarter, p);

    for (int j = 0; j < quarter; ++j) {
        int w = mod_pow(wn, j, p);
        int w2 = (1LL * w * w) % p;
        int w3 = (1LL * w2 * w) % p;

        int x0 = a[j];
        int x1 = (1LL * a[j + quarter] * w) % p;
        int x2 = (1LL * a[j + 2*quarter] * w2) % p;
        int x3 = (1LL * a[j + 3*quarter] * w3) % p;

        int t0 = (x0 + x2) % p;
        int t1 = (x0 - x2 + p) % p;
        int t2 = (x1 + x3) % p;
        int t3 = (1LL * (x1 - x3 + p) * J) % p;

        a[j] = (t0 + t2) % p;
        a[j + quarter] = (t1 + t3) % p;
        a[j + 2*quarter] = (t0 - t2 + p) % p;
        a[j + 3*quarter] = (t1 - t3 + p) % p;
    }
}

void ntt_radix8_mpi(int *a, int n, bool inv, int p, int rank, int size, MPI_Comm comm) {
    if (rank == 0) {
        bitrev(a, n);
    }
    MPI_Bcast(a, n, MPI_INT, 0, comm);

    int lg = __builtin_ctz(n);

    for (int len = 2; len <= n; len <<= 1) {
        int wn = mod_pow(3, (p - 1) / len, p);
        if (inv) wn = mod_pow(wn, p - 2, p);

        int total_blocks = n / len;
        int blocks_per_proc = total_blocks / size;
        int remainder = total_blocks % size;
        int my_blocks = blocks_per_proc + (rank < remainder ? 1 : 0);
        int start_block = rank * blocks_per_proc + min(rank, remainder);

        for (int block = 0; block < my_blocks; ++block) {
            int i = (start_block + block) * len;
            if (i >= n) break;

            int w = 1;
            for (int j = 0; j < len / 2; ++j) {
                int u = a[i + j];
                int v = (1LL * a[i + j + len / 2] * w) % p;
                a[i + j] = (u + v) % p;
                a[i + j + len / 2] = (u - v + p) % p;
                w = (1LL * w * wn) % p;
            }
        }

        vector<int> recvcounts(size);
        vector<int> displs(size);
        for (int r = 0; r < size; ++r) {
            int r_blocks = blocks_per_proc + (r < remainder ? 1 : 0);
            recvcounts[r] = r_blocks * len;
            displs[r] = (r * blocks_per_proc + min(r, remainder)) * len;
        }

        int sendcnt = my_blocks * len;
        if (sendcnt > 0 && start_block * len < n) {
            vector<int> sendbuf(sendcnt);
            memcpy(sendbuf.data(), a + start_block * len, sizeof(int) * min(sendcnt, n - start_block * len));
            MPI_Allgatherv(sendbuf.data(), sendcnt, MPI_INT, a, recvcounts.data(), displs.data(), MPI_INT, comm);
        } else {
            MPI_Allgatherv(nullptr, 0, MPI_INT, a, recvcounts.data(), displs.data(), MPI_INT, comm);
        }
    }

    if (inv) {
        if (rank == 0) {
            int invN = mod_pow(n, p - 2, p);
            for (int i = 0; i < n; ++i) a[i] = (1LL * a[i] * invN) % p;
        }
        MPI_Bcast(a, n, MPI_INT, 0, comm);
    }
}

void poly_mul_radix8_mpi(const int *A_const, const int *B_const, int *C, int n_orig, int p,
                         int rank, int size, MPI_Comm comm) {
    if (n_orig <= 0) return;

    int N = 1;
    while (N < 2 * n_orig) N <<= 1;

    vector<int> pa(N), pb(N);
    for (int i = 0; i < n_orig; ++i) {
        pa[i] = ((A_const[i] % p) + p) % p;
        pb[i] = ((B_const[i] % p) + p) % p;
    }
    for (int i = n_orig; i < N; ++i) {
        pa[i] = 0;
        pb[i] = 0;
    }

    ntt_radix8_mpi(pa.data(), N, false, p, rank, size, comm);
    ntt_radix8_mpi(pb.data(), N, false, p, rank, size, comm);

    int items_per_rank = N / size;
    int remainder = N % size;
    int my_items = items_per_rank + (rank < remainder ? 1 : 0);
    int my_start = rank * items_per_rank + min(rank, remainder);

    vector<int> local_product(max(my_items, 1));
    for (int i = 0; i < my_items; ++i) {
        int global_idx = my_start + i;
        if (global_idx < N) {
            local_product[i] = (1LL * pa[global_idx] * pb[global_idx]) % p;
        } else {
            local_product[i] = 0;
        }
    }

    vector<int> recvcounts(size);
    vector<int> displs(size);
    int current_displ = 0;
    for (int r = 0; r < size; ++r) {
        recvcounts[r] = items_per_rank + (r < remainder ? 1 : 0);
        displs[r] = current_displ;
        current_displ += recvcounts[r];
    }

    MPI_Allgatherv(local_product.data(), my_items, MPI_INT, pa.data(),
                   recvcounts.data(), displs.data(), MPI_INT, comm);

    ntt_radix8_mpi(pa.data(), N, true, p, rank, size, comm);

    if (rank == 0) {
        int result_size = 2 * n_orig - 1;
        if (result_size > 0 && result_size <= N) {
            memcpy(C, pa.data(), sizeof(int) * result_size);
        }
    }
}

int main(int argc, char** argv) {
    MPI_Init(&argc, &argv);
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);

    if (rank == 0) {
        cout << "Radix-8 NTT MPI 实现\n";
        cout << "进程数: " << size << "\n";
        cout << "========================================\n";
    }

    vector<int> a(300000, 0), b(300000, 0), ab(600000, 0);

    for (int id = 0; id <= 3; ++id) {
        int n = 0, p = 0;

        if (rank == 0) {
            fRead(a.data(), b.data(), &n, &p, id);
        }

        MPI_Bcast(&n, 1, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(&p, 1, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(a.data(), n, MPI_INT, 0, MPI_COMM_WORLD);
        MPI_Bcast(b.data(), n, MPI_INT, 0, MPI_COMM_WORLD);

        MPI_Barrier(MPI_COMM_WORLD);
        double t0 = MPI_Wtime();

        poly_mul_radix8_mpi(a.data(), b.data(), ab.data(), n, p, rank, size, MPI_COMM_WORLD);

        MPI_Barrier(MPI_COMM_WORLD);
        double t1 = MPI_Wtime();

        if (rank == 0) {
            fCheck(ab.data(), n, id);
            cout << "测试用例 " << id << " (n=" << n << ", p=" << p << "): "
                 << fixed << setprecision(3) << (t1 - t0) * 1000 << " ms\n";
        }
    }

    MPI_Finalize();
    return 0;
}
