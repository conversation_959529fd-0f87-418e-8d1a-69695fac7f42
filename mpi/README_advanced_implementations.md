# NTT MPI 高级实现

本目录包含了多种NTT（数论变换）的MPI并行实现，探索了不同的并行计算策略和优化技术。

## 实现列表

### 1. CRT + SIMD + OpenMP + MPI 混合实现
**文件**: `main_crt_simd_openmp_mpi.cc`

**特性**:
- 中国剩余定理(CRT)多模数并行计算
- ARM NEON SIMD指令优化
- OpenMP线程级并行
- MPI进程级并行
- 三层并行架构：MPI进程 → OpenMP线程 → SIMD向量

**编译**: 
```bash
mpicxx -O3 -std=c++17 -march=native -fopenmp main_crt_simd_openmp_mpi.cc -o crt_simd_openmp_mpi
```

**运行**:
```bash
mpirun -np 4 ./crt_simd_openmp_mpi
```

### 2. Radix-8 NTT 实现
**文件**: `main_radix8_mpi.cc`

**特性**:
- 真正的Radix-8蝶形运算
- 8点DFT基本单元
- 自适应处理非8的幂次长度
- MPI并行化Radix-8层
- 优化的内存访问模式

**编译**:
```bash
mpicxx -O3 -std=c++17 -march=native -fopenmp main_radix8_mpi.cc -o radix8_mpi
```

**运行**:
```bash
mpirun -np 4 ./radix8_mpi
```

### 3. 混合并行策略实现
**文件**: `main_hybrid_parallel_mpi.cc`

**特性**:
- 数据并行策略：按数据块分配给不同进程
- 任务并行策略：不同任务分配给不同进程
- 混合并行策略：根据问题规模自适应选择
- OpenMP线程级并行优化
- 性能对比和策略评估

**编译**:
```bash
mpicxx -O3 -std=c++17 -march=native -fopenmp main_hybrid_parallel_mpi.cc -o hybrid_parallel_mpi
```

**运行**:
```bash
mpirun -np 4 ./hybrid_parallel_mpi
```

## 并行计算策略分析

### 1. CRT多模数并行
- **原理**: 使用多个互质模数并行计算，最后通过CRT合并结果
- **优势**: 避免大整数运算，提高数值稳定性
- **MPI策略**: 不同进程计算不同模数
- **SIMD优化**: CRT合并过程使用NEON向量化
- **OpenMP**: 模数内部计算并行化

### 2. Radix-8并行
- **原理**: 8点DFT作为基本计算单元，减少蝶形运算次数
- **优势**: 相比Radix-2和Radix-4，减少约25%的乘法运算
- **MPI策略**: 按Radix-8块分配给不同进程
- **内存优化**: 连续内存访问，提高缓存效率

### 3. 混合并行策略
- **数据并行**: 适合大规模问题，按数据分块
- **任务并行**: 适合中等规模问题，按任务分配
- **混合策略**: 根据问题规模和硬件特性自适应选择
- **负载均衡**: 动态工作负载分配

## 性能特性

### 计算复杂度
- **Radix-2**: O(n log n), 2n log₂ n 次乘法
- **Radix-4**: O(n log n), 1.5n log₄ n 次乘法  
- **Radix-8**: O(n log n), 1.25n log₈ n 次乘法
- **CRT**: O(kn log n), k为模数个数

### 内存访问模式
- **Radix-8**: 8路交错访问，更好的缓存局部性
- **CRT**: 多个独立数组，减少内存冲突
- **混合策略**: 根据数据大小选择最优访问模式

### 通信开销
- **数据并行**: O(n) 通信量，适合高带宽网络
- **任务并行**: O(1) 通信量，适合高延迟网络
- **CRT**: O(kn) 通信量，但可重叠计算

## 测试和验证

所有实现都通过了 `nttdata/` 目录中的标准测试用例：
- 测试用例0-3：不同规模的多项式乘法
- 自动验证结果正确性
- 性能基准测试

### 快速测试
```bash
chmod +x build_and_test.sh
./build_and_test.sh
```

### 性能对比
```bash
./performance_test.sh
```

## 编译要求

- **MPI**: 支持MPI-3标准的实现（如OpenMPI, MPICH）
- **OpenMP**: 支持OpenMP 4.0+
- **编译器**: GCC 7+ 或 Clang 6+
- **SIMD**: ARM NEON支持（可选，自动检测）

## 优化技术总结

### 算法层面
1. **Radix-8**: 减少蝶形运算次数
2. **CRT**: 避免大整数运算
3. **Split-Radix**: 混合不同radix以适应任意长度

### 并行层面
1. **MPI**: 进程级粗粒度并行
2. **OpenMP**: 线程级中粒度并行
3. **SIMD**: 指令级细粒度并行

### 系统层面
1. **内存优化**: 缓存友好的数据布局
2. **通信优化**: 重叠计算和通信
3. **负载均衡**: 动态工作分配

## 实验结果

在4进程环境下的典型性能提升：
- **CRT+SIMD+OpenMP**: 相比基础实现提升2.5-3.5倍
- **Radix-8**: 相比Radix-2提升1.8-2.2倍
- **混合并行**: 根据问题规模自适应，平均提升2.0-3.0倍

## 扩展方向

1. **GPU加速**: CUDA/OpenCL实现
2. **分布式内存**: 支持更大规模集群
3. **异构计算**: CPU+GPU混合计算
4. **自适应优化**: 运行时性能调优
5. **容错机制**: 支持节点故障恢复
