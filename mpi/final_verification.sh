#!/bin/bash

echo "=========================================="
echo "NTT MPI 实现最终验证"
echo "=========================================="
echo

# 检查编译文件是否存在
echo "检查编译文件..."
files_exist=true

if [ ! -f "crt_simd_openmp_mpi_final" ]; then
    echo "编译 CRT+SIMD+OpenMP+MPI 实现..."
    mpicxx -O3 -std=c++17 -march=native -fopenmp main_crt_simd_openmp_mpi.cc -o crt_simd_openmp_mpi_final
fi

if [ ! -f "radix8_mpi_final" ]; then
    echo "编译 Radix-8 NTT 实现..."
    mpicxx -O3 -std=c++17 -march=native -fopenmp main_radix8_mpi.cc -o radix8_mpi_final
fi

if [ ! -f "hybrid_parallel_mpi_fixed" ]; then
    echo "编译混合并行策略实现..."
    mpicxx -O3 -std=c++17 -march=native -fopenmp main_hybrid_parallel_mpi.cc -o hybrid_parallel_mpi_fixed
fi

echo "所有文件编译完成"
echo

# 测试函数
test_implementation() {
    local name="$1"
    local executable="$2"
    local processes="$3"
    
    echo "=========================================="
    echo "测试: $name"
    echo "=========================================="
    
    if [ ! -f "$executable" ]; then
        echo "❌ 文件不存在: $executable"
        return 1
    fi
    
    # 运行测试并捕获输出
    output=$(timeout 180 mpirun -np $processes ./$executable 2>/dev/null)
    exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "❌ 执行失败 (退出码: $exit_code)"
        return 1
    fi
    
    # 检查是否所有测试用例都通过
    correct_count=$(echo "$output" | grep -c "多项式乘法结果正确")
    
    if [ $correct_count -eq 4 ]; then
        echo "✅ 全部测试通过 (4/4)"
        echo "$output" | grep "测试用例"
        return 0
    else
        echo "❌ 部分测试失败 ($correct_count/4)"
        echo "$output" | grep -E "(多项式乘法结果|测试用例)"
        return 1
    fi
}

# 执行所有测试
echo "开始验证所有实现..."
echo

total_tests=0
passed_tests=0

# 测试1: CRT+SIMD+OpenMP+MPI
total_tests=$((total_tests + 1))
if test_implementation "CRT+SIMD+OpenMP+MPI" "crt_simd_openmp_mpi_final" 4; then
    passed_tests=$((passed_tests + 1))
fi
echo

# 测试2: Radix-8 NTT
total_tests=$((total_tests + 1))
if test_implementation "Radix-8 NTT" "radix8_mpi_final" 4; then
    passed_tests=$((passed_tests + 1))
fi
echo

# 测试3: 混合并行策略
total_tests=$((total_tests + 1))
if test_implementation "混合并行策略" "hybrid_parallel_mpi_fixed" 4; then
    passed_tests=$((passed_tests + 1))
fi
echo

# 最终结果
echo "=========================================="
echo "最终验证结果"
echo "=========================================="

if [ $passed_tests -eq $total_tests ]; then
    echo "🎉 所有实现验证通过! ($passed_tests/$total_tests)"
    echo
    echo "✅ CRT + SIMD + OpenMP + MPI 实现"
    echo "✅ Radix-8 NTT 实现"  
    echo "✅ 混合并行策略实现"
    echo
    echo "所有实现都通过了全部四个测试用例!"
    echo "项目要求完全达成!"
else
    echo "❌ 部分实现验证失败 ($passed_tests/$total_tests)"
    exit 1
fi

echo
echo "技术特点总结:"
echo "- 多层并行架构: MPI + OpenMP + SIMD"
echo "- 算法创新: CRT多模数 + Radix-8 + 自适应策略"
echo "- 系统优化: 内存访问 + 负载均衡 + 通信优化"
echo "- 严格验证: 通过nttdata全部测试用例"
echo
echo "=========================================="
