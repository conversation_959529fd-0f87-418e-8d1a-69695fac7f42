#!/bin/bash

echo "=== NTT MPI 实现编译和测试脚本 ==="
echo

# 检查MPI环境
if ! command -v mpicxx &> /dev/null; then
    echo "错误: 未找到 mpicxx 编译器"
    exit 1
fi

if ! command -v mpirun &> /dev/null; then
    echo "错误: 未找到 mpirun 命令"
    exit 1
fi

# 编译选项
CXXFLAGS="-O3 -std=c++17 -march=native -fopenmp"
PROCESSES=4

echo "编译选项: $CXXFLAGS"
echo "测试进程数: $PROCESSES"
echo

# 创建输出目录
mkdir -p files

# 1. CRT + SIMD + OpenMP + MPI 实现
echo "1. 编译 CRT + SIMD + OpenMP + MPI 实现..."
if mpicxx $CXXFLAGS main_crt_simd_openmp_mpi.cc -o crt_simd_openmp_mpi; then
    echo "   编译成功"
    echo "   运行测试..."
    echo "   ----------------------------------------"
    mpirun -np $PROCESSES ./crt_simd_openmp_mpi
    echo "   ----------------------------------------"
    echo
else
    echo "   编译失败"
    echo
fi

# 2. Radix-8 NTT 实现
echo "2. 编译 Radix-8 NTT 实现..."
if mpicxx $CXXFLAGS main_radix8_mpi.cc -o radix8_mpi; then
    echo "   编译成功"
    echo "   运行测试..."
    echo "   ----------------------------------------"
    mpirun -np $PROCESSES ./radix8_mpi
    echo "   ----------------------------------------"
    echo
else
    echo "   编译失败"
    echo
fi

# 3. 混合并行策略实现
echo "3. 编译混合并行策略实现..."
if mpicxx $CXXFLAGS main_hybrid_parallel_mpi.cc -o hybrid_parallel_mpi; then
    echo "   编译成功"
    echo "   运行测试..."
    echo "   ----------------------------------------"
    mpirun -np $PROCESSES ./hybrid_parallel_mpi
    echo "   ----------------------------------------"
    echo
else
    echo "   编译失败"
    echo
fi

# 4. 优化的CRT实现（如果存在）
if [ -f "main_crt_optimized_mpi.cc" ]; then
    echo "4. 编译优化的CRT实现..."
    if mpicxx $CXXFLAGS main_crt_optimized_mpi.cc -o crt_optimized_mpi; then
        echo "   编译成功"
        echo "   运行测试..."
        echo "   ----------------------------------------"
        mpirun -np $PROCESSES ./crt_optimized_mpi
        echo "   ----------------------------------------"
        echo
    else
        echo "   编译失败"
        echo
    fi
fi

# 5. 现有的Radix-4实现对比
if [ -f "main_barrett_radix4_ntt.cc" ]; then
    echo "5. 编译现有的Radix-4实现（对比）..."
    if mpicxx $CXXFLAGS main_barrett_radix4_ntt.cc -o barrett_radix4_mpi; then
        echo "   编译成功"
        echo "   运行测试..."
        echo "   ----------------------------------------"
        mpirun -np $PROCESSES ./barrett_radix4_mpi
        echo "   ----------------------------------------"
        echo
    else
        echo "   编译失败"
        echo
    fi
fi

echo "=== 性能对比测试 ==="
echo

# 性能对比脚本
cat > performance_test.sh << 'EOF'
#!/bin/bash

echo "性能对比测试 (4进程)"
echo "测试用例: nttdata/0.in - nttdata/3.in"
echo

PROCESSES=4

echo "1. CRT + SIMD + OpenMP + MPI:"
if [ -f "./crt_simd_openmp_mpi" ]; then
    time mpirun -np $PROCESSES ./crt_simd_openmp_mpi 2>/dev/null | grep "测试用例\|多项式乘法结果"
else
    echo "   未编译"
fi
echo

echo "2. Radix-8 NTT:"
if [ -f "./radix8_mpi" ]; then
    time mpirun -np $PROCESSES ./radix8_mpi 2>/dev/null | grep "测试用例\|多项式乘法结果"
else
    echo "   未编译"
fi
echo

echo "3. 混合并行策略:"
if [ -f "./hybrid_parallel_mpi" ]; then
    time mpirun -np $PROCESSES ./hybrid_parallel_mpi 2>/dev/null | grep "测试用例\|多项式乘法结果\|最优策略"
else
    echo "   未编译"
fi
echo

echo "4. 优化的CRT (对比):"
if [ -f "./crt_optimized_mpi" ]; then
    time mpirun -np $PROCESSES ./crt_optimized_mpi 2>/dev/null | grep "测试用例\|多项式乘法结果"
else
    echo "   未编译"
fi
echo

echo "5. Radix-4 (对比):"
if [ -f "./barrett_radix4_mpi" ]; then
    time mpirun -np $PROCESSES ./barrett_radix4_mpi 2>/dev/null | grep "测试用例\|多项式乘法结果"
else
    echo "   未编译"
fi
echo
EOF

chmod +x performance_test.sh

echo "性能对比脚本已创建: performance_test.sh"
echo "运行 ./performance_test.sh 进行性能对比"
echo

# 清理脚本
cat > cleanup.sh << 'EOF'
#!/bin/bash
echo "清理编译文件..."
rm -f crt_simd_openmp_mpi radix8_mpi hybrid_parallel_mpi crt_optimized_mpi barrett_radix4_mpi
rm -f performance_test.sh cleanup.sh
rm -rf files
echo "清理完成"
EOF

chmod +x cleanup.sh

echo "清理脚本已创建: cleanup.sh"
echo "运行 ./cleanup.sh 清理编译文件"
echo

echo "=== 编译和测试完成 ==="
echo "所有实现已编译并测试完成"
echo "查看 files/ 目录中的输出文件"
echo
