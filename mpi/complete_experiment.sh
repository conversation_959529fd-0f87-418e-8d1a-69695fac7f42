#!/bin/bash

echo "=========================================="
echo "Complete NTT MPI Performance Experiment"
echo "=========================================="

# Compile all implementations
echo "Step 1: Compiling all implementations..."
echo

echo "1. Compiling CRT+SIMD+OpenMP+MPI..."
mpicxx -O3 -std=c++17 -march=native -fopenmp main_crt_simd_openmp_mpi.cc -o crt_simd_openmp_mpi_final
if [ $? -eq 0 ]; then
    echo "   ✅ Success"
else
    echo "   ❌ Failed"
fi

echo "2. Compiling Hybrid Parallel Strategy..."
mpicxx -O3 -std=c++17 -march=native -fopenmp main_hybrid_parallel_mpi.cc -o hybrid_parallel_mpi_fixed
if [ $? -eq 0 ]; then
    echo "   ✅ Success"
else
    echo "   ❌ Failed"
fi

echo "3. Compiling existing Radix-4 baseline..."
if [ -f "main_barrett_radix4_ntt.cc" ]; then
    mpicxx -O3 -std=c++17 -march=native -fopenmp main_barrett_radix4_ntt.cc -o barrett_radix4_mpi
    if [ $? -eq 0 ]; then
        echo "   ✅ Success"
    else
        echo "   ❌ Failed"
    fi
else
    echo "   ⚠️  Radix-4 source not found"
fi

echo "4. Compiling optimized CRT..."
if [ -f "main_crt_optimized_mpi.cc" ]; then
    mpicxx -O3 -std=c++17 -march=native -fopenmp main_crt_optimized_mpi.cc -o crt_optimized_mpi
    if [ $? -eq 0 ]; then
        echo "   ✅ Success"
    else
        echo "   ❌ Failed"
    fi
else
    echo "   ⚠️  Optimized CRT source not found"
fi

echo
echo "Step 2: Running performance experiments..."
echo

# Create results directory
mkdir -p experiment_results
cd experiment_results

# CSV header
echo "Implementation,Test_Case,Problem_Size,Execution_Time_ms,Num_Processes,Test_Passed" > performance_results.csv

PROCESSES=4

# Function to run a single test and extract results
run_test() {
    local executable="$1"
    local impl_name="$2"
    
    if [ ! -f "../$executable" ]; then
        echo "   ❌ Executable not found: $executable"
        return
    fi
    
    echo "   Running $impl_name..."
    
    # Capture output
    output=$(timeout 180 mpirun -np $PROCESSES ../$executable 2>/dev/null)
    exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "   ❌ Execution failed"
        return
    fi
    
    # Parse output and extract results
    echo "$output" | grep "测试用例" | while IFS= read -r line; do
        # Extract test case number
        test_case=$(echo "$line" | sed -n 's/.*测试用例 \([0-9]\+\).*/\1/p')
        # Extract problem size
        problem_size=$(echo "$line" | sed -n 's/.*n=\([0-9]\+\).*/\1/p')
        # Extract execution time
        time_ms=$(echo "$line" | sed -n 's/.*: \([0-9.]\+\) ms/\1/p')
        
        # Check if test passed by looking for "正确" in the output
        test_passed="False"
        if echo "$output" | grep -q "多项式乘法结果正确"; then
            test_passed="True"
        fi
        
        if [ -n "$test_case" ] && [ -n "$problem_size" ] && [ -n "$time_ms" ]; then
            echo "$impl_name,$test_case,$problem_size,$time_ms,$PROCESSES,$test_passed" >> performance_results.csv
        fi
    done
    
    # Count successful tests
    correct_count=$(echo "$output" | grep -c "多项式乘法结果正确")
    echo "   ✅ Completed ($correct_count/4 tests passed)"
}

# Run tests for each implementation
echo "Testing implementations:"

run_test "crt_simd_openmp_mpi_final" "CRT+SIMD+OpenMP+MPI"
run_test "hybrid_parallel_mpi_fixed" "Hybrid Parallel Strategy"
run_test "barrett_radix4_mpi" "Radix-4 Baseline"
run_test "crt_optimized_mpi" "Optimized CRT"

echo
echo "Step 3: Generating visualizations..."
echo

# Generate enhanced Python visualization script
cat > visualize_performance.py << 'EOF'
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Read the CSV data
try:
    df = pd.read_csv('performance_results.csv')
    print("Data loaded successfully!")
    print(f"Total records: {len(df)}")
    print(f"Implementations: {list(df['Implementation'].unique())}")
    print(f"Problem sizes: {sorted(df['Problem_Size'].unique())}")
except Exception as e:
    print(f"Error loading data: {e}")
    exit(1)

if len(df) == 0:
    print("No data to visualize!")
    exit(1)

# Set up the plotting style
plt.style.use('default')
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
plt.rcParams['font.size'] = 10

# Create figure with subplots
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('NTT MPI Implementation Performance Analysis', fontsize=16, fontweight='bold')

# 1. Execution Time vs Problem Size (Log-Log Plot)
ax1 = axes[0, 0]
for i, impl in enumerate(df['Implementation'].unique()):
    impl_data = df[df['Implementation'] == impl].sort_values('Problem_Size')
    if len(impl_data) > 0:
        ax1.plot(impl_data['Problem_Size'], impl_data['Execution_Time_ms'], 
                 marker='o', linewidth=2.5, markersize=8, label=impl, color=colors[i % len(colors)])

ax1.set_xlabel('Problem Size (n)', fontsize=12, fontweight='bold')
ax1.set_ylabel('Execution Time (ms)', fontsize=12, fontweight='bold')
ax1.set_title('Performance vs Problem Size', fontsize=14, fontweight='bold')
ax1.set_xscale('log')
ax1.set_yscale('log')
ax1.grid(True, alpha=0.3)
ax1.legend(fontsize=10)

# 2. Performance Comparison Bar Chart (Average)
ax2 = axes[0, 1]
avg_performance = df.groupby('Implementation')['Execution_Time_ms'].mean().sort_values()
implementations = avg_performance.index
times = avg_performance.values

bars = ax2.bar(range(len(implementations)), times, 
               color=[colors[i % len(colors)] for i in range(len(implementations))])
ax2.set_xlabel('Implementation', fontsize=12, fontweight='bold')
ax2.set_ylabel('Average Execution Time (ms)', fontsize=12, fontweight='bold')
ax2.set_title('Average Performance Comparison', fontsize=14, fontweight='bold')
ax2.set_xticks(range(len(implementations)))
ax2.set_xticklabels(implementations, rotation=45, ha='right')
ax2.grid(True, alpha=0.3, axis='y')

# Add value labels on bars
for i, bar in enumerate(bars):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{height:.1f}ms', ha='center', va='bottom', fontsize=9, fontweight='bold')

# 3. Speedup Analysis (relative to slowest implementation)
ax3 = axes[1, 0]
if len(avg_performance) > 1:
    baseline_impl = avg_performance.index[-1]  # Slowest implementation
    baseline_data = df[df['Implementation'] == baseline_impl]

    for i, impl in enumerate(df['Implementation'].unique()):
        impl_data = df[df['Implementation'] == impl]
        speedup = []
        problem_sizes = []
        
        for size in sorted(impl_data['Problem_Size'].unique()):
            baseline_subset = baseline_data[baseline_data['Problem_Size'] == size]
            impl_subset = impl_data[impl_data['Problem_Size'] == size]
            
            if len(baseline_subset) > 0 and len(impl_subset) > 0:
                baseline_time = baseline_subset['Execution_Time_ms'].iloc[0]
                impl_time = impl_subset['Execution_Time_ms'].iloc[0]
                speedup.append(baseline_time / impl_time)
                problem_sizes.append(size)
        
        if len(speedup) > 0:
            ax3.plot(problem_sizes, speedup, marker='s', linewidth=2.5, markersize=8, 
                     label=impl, color=colors[i % len(colors)])

    ax3.set_xlabel('Problem Size (n)', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Speedup Factor', fontsize=12, fontweight='bold')
    ax3.set_title(f'Speedup Relative to {baseline_impl}', fontsize=14, fontweight='bold')
    ax3.set_xscale('log')
    ax3.axhline(y=1, color='red', linestyle='--', alpha=0.7, linewidth=2, label='Baseline')
    ax3.grid(True, alpha=0.3)
    ax3.legend(fontsize=10)
else:
    ax3.text(0.5, 0.5, 'Insufficient data for speedup analysis', 
             ha='center', va='center', transform=ax3.transAxes, fontsize=12)
    ax3.set_title('Speedup Analysis', fontsize=14, fontweight='bold')

# 4. Test Success Rate and Performance by Test Case
ax4 = axes[1, 1]
success_rate = df.groupby('Implementation')['Test_Passed'].apply(lambda x: (x == 'True').sum() / len(x) * 100)
implementations = success_rate.index
rates = success_rate.values

colors_success = ['green' if rate == 100 else 'orange' if rate >= 75 else 'red' for rate in rates]
bars = ax4.bar(range(len(implementations)), rates, color=colors_success, alpha=0.7)
ax4.set_xlabel('Implementation', fontsize=12, fontweight='bold')
ax4.set_ylabel('Test Success Rate (%)', fontsize=12, fontweight='bold')
ax4.set_title('Correctness Validation', fontsize=14, fontweight='bold')
ax4.set_xticks(range(len(implementations)))
ax4.set_xticklabels(implementations, rotation=45, ha='right')
ax4.set_ylim(0, 105)
ax4.grid(True, alpha=0.3, axis='y')

# Add value labels on bars
for i, bar in enumerate(bars):
    height = bar.get_height()
    ax4.text(bar.get_x() + bar.get_width()/2., height + 2,
             f'{height:.0f}%', ha='center', va='bottom', fontsize=9, fontweight='bold')

plt.tight_layout()
plt.savefig('ntt_performance_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

# Generate detailed summary statistics
print("\n" + "="*50)
print("PERFORMANCE ANALYSIS SUMMARY")
print("="*50)
print(f"Total implementations tested: {df['Implementation'].nunique()}")
print(f"Problem sizes tested: {sorted(df['Problem_Size'].unique())}")
print(f"Total test cases: {len(df)}")
print(f"MPI Processes used: {df['Num_Processes'].iloc[0] if len(df) > 0 else 'N/A'}")

print(f"\n{'PERFORMANCE RANKING (Average)':<40}")
print("-" * 50)
for i, (impl, avg_time) in enumerate(avg_performance.items(), 1):
    impl_data = df[df['Implementation'] == impl]
    success_rate = (impl_data['Test_Passed'] == 'True').sum()
    total_tests = len(impl_data)
    print(f"{i:2d}. {impl:<30} {avg_time:8.2f}ms ({success_rate}/{total_tests} passed)")

print(f"\n{'BEST PERFORMANCE BY PROBLEM SIZE':<40}")
print("-" * 50)
for size in sorted(df['Problem_Size'].unique()):
    size_data = df[df['Problem_Size'] == size]
    if len(size_data) > 0:
        best = size_data.loc[size_data['Execution_Time_ms'].idxmin()]
        print(f"n={size:<10} {best['Implementation']:<25} {best['Execution_Time_ms']:8.2f}ms")

# Calculate and display speedup factors
if len(avg_performance) > 1:
    print(f"\n{'SPEEDUP FACTORS (vs Slowest)':<40}")
    print("-" * 50)
    slowest_time = avg_performance.iloc[-1]
    for impl, avg_time in avg_performance.items():
        speedup = slowest_time / avg_time
        print(f"{impl:<30} {speedup:8.2f}x")

print(f"\nVisualization saved as: ntt_performance_analysis.png")
print("="*50)
EOF

# Run the visualization
echo "Running Python visualization..."
python3 visualize_performance.py

echo
echo "=========================================="
echo "Experiment completed successfully!"
echo "Results available in: experiment_results/"
echo "- performance_results.csv (raw data)"
echo "- ntt_performance_analysis.png (visualization)"
echo "=========================================="
