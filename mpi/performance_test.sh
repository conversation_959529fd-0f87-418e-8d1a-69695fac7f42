#!/bin/bash

echo "性能对比测试 (4进程)"
echo "测试用例: nttdata/0.in - nttdata/3.in"
echo

PROCESSES=4

echo "1. CRT + SIMD + OpenMP + MPI:"
if [ -f "./crt_simd_openmp_mpi" ]; then
    time mpirun -np $PROCESSES ./crt_simd_openmp_mpi 2>/dev/null | grep "测试用例\|多项式乘法结果"
else
    echo "   未编译"
fi
echo

echo "2. Radix-8 NTT:"
if [ -f "./radix8_mpi" ]; then
    time mpirun -np $PROCESSES ./radix8_mpi 2>/dev/null | grep "测试用例\|多项式乘法结果"
else
    echo "   未编译"
fi
echo

echo "3. 混合并行策略:"
if [ -f "./hybrid_parallel_mpi" ]; then
    time mpirun -np $PROCESSES ./hybrid_parallel_mpi 2>/dev/null | grep "测试用例\|多项式乘法结果\|最优策略"
else
    echo "   未编译"
fi
echo

echo "4. 优化的CRT (对比):"
if [ -f "./crt_optimized_mpi" ]; then
    time mpirun -np $PROCESSES ./crt_optimized_mpi 2>/dev/null | grep "测试用例\|多项式乘法结果"
else
    echo "   未编译"
fi
echo

echo "5. Radix-4 (对比):"
if [ -f "./barrett_radix4_mpi" ]; then
    time mpirun -np $PROCESSES ./barrett_radix4_mpi 2>/dev/null | grep "测试用例\|多项式乘法结果"
else
    echo "   未编译"
fi
echo
