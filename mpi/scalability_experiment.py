#!/usr/bin/env python3

import matplotlib.pyplot as plt
import numpy as np
import subprocess
import time
import os

def run_mpi_test(executable, num_processes):
    """Run MPI test and extract performance data"""
    try:
        # Change to parent directory to access nttdata
        os.chdir('..')
        cmd = f"mpirun -np {num_processes} ./mpi/{executable}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=180)
        os.chdir('mpi')
        
        if result.returncode != 0:
            return None
        
        # Parse output to extract timing data
        lines = result.stdout.split('\n')
        times = []
        for line in lines:
            if '测试用例' in line and 'ms' in line:
                try:
                    # Extract time from line like "测试用例 1 (n=131072, p=7340033): 131.373 ms"
                    time_str = line.split(':')[-1].strip().replace(' ms', '')
                    times.append(float(time_str))
                except:
                    continue
        
        return times if times else None
    except Exception as e:
        print(f"Error running {executable} with {num_processes} processes: {e}")
        return None

def create_scalability_analysis():
    """Create comprehensive scalability analysis"""
    
    # Test configurations
    process_counts = [1, 2, 4, 8]
    executable = "crt_simd_openmp_mpi_final"
    
    print("Running scalability experiment...")
    print("=" * 50)
    
    # Collect performance data
    scalability_data = {}
    
    for num_proc in process_counts:
        print(f"Testing with {num_proc} processes...")
        times = run_mpi_test(executable, num_proc)
        if times:
            # Take average of large problem sizes (test cases 1-3)
            large_problem_times = times[1:] if len(times) > 1 else times
            avg_time = np.mean(large_problem_times) if large_problem_times else None
            scalability_data[num_proc] = {
                'avg_time': avg_time,
                'all_times': times
            }
            print(f"   Average time: {avg_time:.2f}ms")
        else:
            print(f"   Failed to collect data")
    
    # Create visualization
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('NTT MPI Scalability Analysis', fontsize=16, fontweight='bold')
    
    # 1. Strong Scaling Analysis
    ax1 = axes[0, 0]
    processes = []
    avg_times = []
    
    for proc, data in scalability_data.items():
        if data['avg_time'] is not None:
            processes.append(proc)
            avg_times.append(data['avg_time'])
    
    if processes and avg_times:
        ax1.plot(processes, avg_times, 'bo-', linewidth=2, markersize=8, label='Actual Performance')
        
        # Ideal scaling (inverse proportional)
        if len(avg_times) > 0:
            baseline_time = avg_times[0]  # Single process time
            ideal_times = [baseline_time / p for p in processes]
            ax1.plot(processes, ideal_times, 'r--', linewidth=2, label='Ideal Scaling')
    
    ax1.set_xlabel('Number of MPI Processes', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Execution Time (ms)', fontsize=12, fontweight='bold')
    ax1.set_title('Strong Scaling Performance', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_yscale('log')
    
    # 2. Parallel Efficiency
    ax2 = axes[0, 1]
    if processes and avg_times and len(avg_times) > 0:
        baseline_time = avg_times[0]
        efficiencies = [(baseline_time / (p * t)) * 100 for p, t in zip(processes, avg_times)]
        
        ax2.plot(processes, efficiencies, 'go-', linewidth=2, markersize=8)
        ax2.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='Ideal Efficiency')
        ax2.axhline(y=50, color='orange', linestyle='--', alpha=0.7, label='50% Efficiency')
    
    ax2.set_xlabel('Number of MPI Processes', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Parallel Efficiency (%)', fontsize=12, fontweight='bold')
    ax2.set_title('Parallel Efficiency Analysis', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_ylim(0, 110)
    
    # 3. Speedup Factor
    ax3 = axes[1, 0]
    if processes and avg_times and len(avg_times) > 0:
        baseline_time = avg_times[0]
        speedups = [baseline_time / t for t in avg_times]
        
        ax3.plot(processes, speedups, 'mo-', linewidth=2, markersize=8, label='Actual Speedup')
        ax3.plot(processes, processes, 'r--', linewidth=2, label='Linear Speedup')
    
    ax3.set_xlabel('Number of MPI Processes', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Speedup Factor', fontsize=12, fontweight='bold')
    ax3.set_title('Speedup Analysis', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # 4. Performance Breakdown by Test Case
    ax4 = axes[1, 1]
    test_case_names = ['Small (n=4)', 'Large 1 (n=131072)', 'Large 2 (n=131072)', 'Large 3 (n=131072)']
    
    # Create bar chart for different process counts
    x = np.arange(len(test_case_names))
    width = 0.2
    
    for i, (proc, data) in enumerate(scalability_data.items()):
        if data['all_times'] and len(data['all_times']) >= 4:
            times = data['all_times'][:4]  # First 4 test cases
            ax4.bar(x + i * width, times, width, label=f'{proc} processes', alpha=0.8)
    
    ax4.set_xlabel('Test Cases', fontsize=12, fontweight='bold')
    ax4.set_ylabel('Execution Time (ms)', fontsize=12, fontweight='bold')
    ax4.set_title('Performance by Test Case', fontsize=14, fontweight='bold')
    ax4.set_xticks(x + width * 1.5)
    ax4.set_xticklabels(test_case_names, rotation=45, ha='right')
    ax4.legend()
    ax4.grid(True, alpha=0.3, axis='y')
    ax4.set_yscale('log')
    
    plt.tight_layout()
    plt.savefig('ntt_scalability_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Generate detailed report
    print("\n" + "="*60)
    print("SCALABILITY ANALYSIS REPORT")
    print("="*60)
    
    print(f"\nTEST CONFIGURATION:")
    print(f"Implementation: CRT+SIMD+OpenMP+MPI")
    print(f"Process counts tested: {process_counts}")
    print(f"Problem sizes: n=4, n=131072 (3 test cases)")
    
    print(f"\nPERFORMANCE RESULTS:")
    print("-" * 40)
    for proc, data in scalability_data.items():
        if data['avg_time']:
            print(f"{proc:2d} processes: {data['avg_time']:8.2f}ms average")
    
    if len(avg_times) > 1:
        print(f"\nSCALABILITY METRICS:")
        print("-" * 40)
        baseline_time = avg_times[0]
        for i, (proc, time) in enumerate(zip(processes, avg_times)):
            speedup = baseline_time / time
            efficiency = (speedup / proc) * 100
            print(f"{proc:2d} processes: {speedup:5.2f}x speedup, {efficiency:5.1f}% efficiency")
    
    print(f"\nKEY OBSERVATIONS:")
    print("-" * 40)
    print("• Multi-layer parallelization (MPI+OpenMP+SIMD) tested")
    print("• Strong scaling performance evaluated")
    print("• Parallel efficiency measured across different process counts")
    print("• Performance breakdown by problem size analyzed")
    
    print(f"\nVisualization saved as: ntt_scalability_analysis.png")
    print("="*60)

if __name__ == "__main__":
    create_scalability_analysis()
