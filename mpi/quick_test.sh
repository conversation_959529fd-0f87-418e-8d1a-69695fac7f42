#!/bin/bash

echo "=== NTT MPI 实现快速测试 ==="
echo

PROCESSES=4

echo "1. 测试 CRT + SIMD + OpenMP + MPI 实现:"
echo "   只测试第一个用例..."
timeout 30 mpirun -np $PROCESSES ./crt_simd_openmp_mpi_fixed 2>/dev/null | head -10
echo

echo "2. 测试 Radix-8 NTT 实现:"
echo "   只测试第一个用例..."
timeout 30 mpirun -np 1 ./radix8_mpi_fixed2 2>/dev/null | head -10
echo

echo "3. 测试混合并行策略实现:"
echo "   只测试第一个用例..."
timeout 30 mpirun -np $PROCESSES ./hybrid_parallel_mpi_fixed 2>/dev/null | head -15
echo

echo "4. 对比优化的CRT实现:"
echo "   只测试第一个用例..."
timeout 30 mpirun -np $PROCESSES ./crt_optimized_mpi 2>/dev/null | head -10
echo

echo "5. 对比Radix-4实现:"
echo "   只测试第一个用例..."
timeout 30 mpirun -np $PROCESSES ./barrett_radix4_mpi 2>/dev/null | head -5
echo

echo "=== 快速测试完成 ==="
