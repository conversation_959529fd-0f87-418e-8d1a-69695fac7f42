#!/bin/bash

echo "=========================================="
echo "NTT MPI Performance Data Collection"
echo "=========================================="

# Create results directory
mkdir -p experiment_results
cd experiment_results

# CSV header
echo "Implementation,Test_Case,Problem_Size,Execution_Time_ms,Num_Processes,Test_Passed" > performance_results.csv

PROCESSES=4

echo "Collecting performance data with $PROCESSES processes..."
echo

# Function to run test and parse output
collect_data() {
    local executable="$1"
    local impl_name="$2"
    
    echo "Testing: $impl_name"
    
    if [ ! -f "../../mpi/$executable" ]; then
        echo "   ❌ Executable not found: $executable"
        return
    fi
    
    # Run from root directory to access nttdata
    output=$(cd ../.. && timeout 180 mpirun -np $PROCESSES ./mpi/$executable 2>/dev/null)
    exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "   ❌ Execution failed"
        return
    fi
    
    # Parse each test case result
    test_case=0
    while IFS= read -r line; do
        if echo "$line" | grep -q "多项式乘法结果正确"; then
            test_passed="True"
        elif echo "$line" | grep -q "多项式乘法结果错误"; then
            test_passed="False"
        elif echo "$line" | grep -q "测试用例"; then
            # Extract data from line like: "测试用例 0 (n=4, p=7340033): 47.252 ms"
            problem_size=$(echo "$line" | sed -n 's/.*n=\([0-9]\+\).*/\1/p')
            time_ms=$(echo "$line" | sed -n 's/.*: \([0-9.]\+\) ms/\1/p')
            test_case_num=$(echo "$line" | sed -n 's/.*测试用例 \([0-9]\+\).*/\1/p')
            
            if [ -n "$problem_size" ] && [ -n "$time_ms" ] && [ -n "$test_case_num" ]; then
                echo "$impl_name,$test_case_num,$problem_size,$time_ms,$PROCESSES,$test_passed" >> performance_results.csv
                echo "   Test $test_case_num: n=$problem_size, time=${time_ms}ms, passed=$test_passed"
            fi
        fi
    done <<< "$output"
    
    echo "   ✅ Completed"
    echo
}

# Collect data from all implementations
collect_data "crt_simd_openmp_mpi_final" "CRT+SIMD+OpenMP+MPI"
collect_data "hybrid_parallel_mpi_fixed" "Hybrid Parallel Strategy"
collect_data "barrett_radix4_mpi" "Radix-4 Baseline"
collect_data "crt_optimized_mpi" "Optimized CRT"

echo "Data collection completed!"
echo "Results saved to: performance_results.csv"
echo

# Show collected data
echo "Collected data:"
cat performance_results.csv
echo
